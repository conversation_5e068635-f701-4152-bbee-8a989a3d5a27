import type { BookingItem, BookingFilters, ViewMode } from '../types'
import { bookingAPIs } from '../apis/booking.api'
import { addDays, addMonths, isAfter, isBefore, startOfDay } from 'date-fns'
import { useCallback, useEffect, useState } from 'react'
import { toast } from 'sonner'

/**
 * Custom hook to fetch and manage bookings
 */
export const useBookings = () => {
  const [bookings, setBookings] = useState<BookingItem[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [bookingPageId, setBookingPageId] = useState<string>('')
  const [viewMode, setViewMode] = useState<ViewMode>('week')
  const [currentPage, setCurrentPage] = useState(1)
  const [dateRange, setDateRange] = useState<{ from: Date, to: Date }>({
    from: addDays(new Date(), -7),
    to: new Date(),
  })

  const itemsPerPage = 10

  // Fetch bookings from API
  const fetchBookings = useCallback(async () => {
    try {
      setIsLoading(true)
      setError(null)

      const params = {
        bookingPageId: bookingPageId || undefined,
        status: statusFilter === 'all' ? undefined : statusFilter,
        startDate: dateRange.from.toISOString().split('T')[0],
        endDate: dateRange.to.toISOString().split('T')[0],
        search: searchQuery || undefined,
        limit: 1000, // Get all for client-side filtering
        offset: 0,
      }

      const response = await bookingAPIs.getBookings(params)
      
      if (response.status?.success && response.data) {
        setBookings(response.data.bookings || [])
      } else {
        throw new Error(response.status?.message || 'Failed to fetch bookings')
      }
    } catch (err: any) {
      console.error('Error fetching bookings:', err)
      setError(err.message || 'Failed to fetch bookings')
      toast.error('Không thể tải danh sách đặt lịch')
    } finally {
      setIsLoading(false)
    }
  }, [bookingPageId, statusFilter, dateRange, searchQuery])

  // Filter bookings based on current filters
  const filteredBookings = bookings.filter((booking) => {
    const bookingDate = new Date(booking.bookingDate)
    const fromDate = startOfDay(dateRange.from)
    const toDate = new Date(dateRange.to)
    toDate.setHours(23, 59, 59, 999)

    // Date range filter
    const isInDateRange = isAfter(bookingDate, fromDate) && isBefore(bookingDate, toDate)

    // Status filter
    const matchesStatus = statusFilter === 'all' || booking.status === statusFilter

    // Search filter
    const matchesSearch = !searchQuery || 
      booking.customerName.toLowerCase().includes(searchQuery.toLowerCase()) ||
      booking.customerPhone.includes(searchQuery) ||
      booking.customerEmail.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (booking.bookingPageName && booking.bookingPageName.toLowerCase().includes(searchQuery.toLowerCase()))

    // Booking page filter
    const matchesBookingPage = !bookingPageId || booking.bookingPageId === bookingPageId

    return isInDateRange && matchesStatus && matchesSearch && matchesBookingPage
  })

  // Pagination logic
  const totalPages = Math.ceil(filteredBookings.length / itemsPerPage)
  const paginatedBookings = filteredBookings.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  )

  // Handle view mode change
  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode)
    const today = new Date()

    let newDateRange = { from: today, to: today }

    switch (mode) {
      case 'day':
        newDateRange = { from: today, to: today }
        break
      case 'week':
        newDateRange = { from: addDays(today, -7), to: today }
        break
      case 'month':
        newDateRange = { from: addMonths(today, -1), to: today }
        break
    }

    setDateRange(newDateRange)
  }

  // Handle date range change
  const handleDateRangeChange = (range: { from: Date, to: Date } | undefined) => {
    if (range) {
      setDateRange(range)
    }
  }

  // Update booking status
  const updateBookingStatus = useCallback(async (bookingId: string, status: 'confirmed' | 'cancelled' | 'rejected') => {
    try {
      await bookingAPIs.updateBookingStatus(bookingId, { status })
      
      // Update local state
      setBookings(prev => prev.map(booking => 
        booking._id === bookingId ? { ...booking, status } : booking
      ))
      
      toast.success(`Đã ${status === 'confirmed' ? 'xác nhận' : status === 'cancelled' ? 'hủy' : 'từ chối'} đặt lịch`)
    } catch (err: any) {
      console.error('Error updating booking status:', err)
      toast.error('Không thể cập nhật trạng thái đặt lịch')
    }
  }, [])

  // Initial fetch
  useEffect(() => {
    fetchBookings()
  }, [fetchBookings])

  // Reset page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [searchQuery, statusFilter, bookingPageId, dateRange])

  return {
    bookings: paginatedBookings,
    isLoading,
    error,
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    bookingPageId,
    setBookingPageId,
    dateRange,
    handleDateRangeChange,
    viewMode,
    handleViewModeChange,
    currentPage,
    setCurrentPage,
    totalPages,
    fetchBookings,
    updateBookingStatus,
    hasFilters: searchQuery !== '' || statusFilter !== 'all' || bookingPageId !== '',
    totalBookings: filteredBookings.length,
  }
}

export default useBookings
