# Manage Bookings Module

This module provides a comprehensive booking management system for the admin panel.

## Structure

```
src/modules/admin/manage-bookings/
├── screens/
│   └── ManageBookingsScreen.tsx     # Main screen component
├── components/
│   ├── BookingCard.tsx              # Individual booking card
│   ├── BookingCardSkeleton.tsx      # Loading skeleton
│   ├── BookingFilters.tsx           # Filter components
│   ├── BookingList.tsx              # List container
│   ├── BookingPagination.tsx        # Pagination component
│   └── EmptyState.tsx               # Empty state component
├── hooks/
│   └── useBookings.ts               # Main data management hook
├── apis/
│   └── booking.api.ts               # API functions
├── types/
│   └── index.ts                     # Type definitions
├── index.ts                         # Module exports
└── README.md                        # This file
```

## Features

- **Real API Integration**: Uses `{{host_mindflow}}/agent/booking` endpoint
- **Advanced Filtering**: Search, status, date range, and booking page filters
- **Pagination**: Client-side pagination with configurable page size
- **Real-time Updates**: Automatic refresh and status updates
- **Manual Booking Creation**: Create bookings manually through admin interface
- **Responsive Design**: Mobile-friendly card-based layout
- **Loading States**: Skeleton components for better UX
- **Error Handling**: Comprehensive error states with retry functionality

## API Endpoints

### Get Bookings
```
GET /agent/booking
Query Parameters:
- bookingPageId?: string
- status?: string
- startDate?: string (YYYY-MM-DD)
- endDate?: string (YYYY-MM-DD)
- search?: string
- limit?: number
- offset?: number
```

### Update Booking Status
```
PUT /agent/booking/{id}/status
Body: { status: 'confirmed' | 'cancelled' | 'rejected' }
```

### Create Manual Booking
```
POST /agent/booking/manual
Body: CreateManualBookingPayload
```

## Usage

```tsx
import { ManageBookingsScreen } from '@/modules/admin/manage-bookings'

export default function BookingsPage() {
  return <ManageBookingsScreen />
}
```

## Components

### BookingCard
Displays individual booking information with action buttons for status updates.

### BookingFilters
Provides filtering capabilities including:
- Date range picker
- Status filter
- Search functionality
- View mode selector (day/week/month)

### BookingList
Container component that handles:
- Loading states
- Error states
- Empty states
- Pagination
- Booking cards rendering

## Hooks

### useBookings
Main hook that manages:
- Data fetching from API
- Client-side filtering
- Pagination logic
- Status updates
- Error handling

## Types

All TypeScript interfaces are defined in `types/index.ts` including:
- `BookingItem`: Main booking data structure
- `BookingFilters`: Filter state interface
- `BookingListProps`: Component props
- `ManualBookingData`: Manual booking form data

## Migration from Old Structure

The old `ManageBookingsScreen.tsx` in `src/modules/admin/screens/` has been replaced with this modular structure that:
- Removes dependency on mock data
- Implements real API integration
- Provides better code organization
- Follows the same pattern as `manage-booking-pages` module
- Improves maintainability and extensibility
