'use client'

import type { ManualBookingData } from '@/modules/admin/components/ManualBookingForm'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { ManualBookingForm } from '@/modules/admin/components/ManualBookingForm'
import { Plus } from 'lucide-react'
import React, { useState } from 'react'
import { toast } from 'sonner'
import { bookingAPIs } from '../apis/booking.api'
import BookingFilters from '../components/BookingFilters'
import BookingList from '../components/BookingList'
import useBookings from '../hooks/useBookings'

/**
 * Screen component for managing bookings
 */
const ManageBookingsScreen = () => {
  const [openManualDialog, setOpenManualDialog] = useState(false)

  const {
    bookings,
    bookingPages,
    isLoading,
    isLoadingBookingPages,
    error,
    searchQuery,
    setSearchQuery,
    statusFilter,
    setStatusFilter,
    bookingPageId,
    setBookingPageId,
    dateRange,
    handleDateRangeChange,
    viewMode,
    handleViewModeChange,
    currentPage,
    setCurrentPage,
    totalPages,
    fetchBookings,
    updateBookingStatus,
    hasFilters,
    totalBookings,
  } = useBookings()

  // Handler for manual booking creation
  const handleManualBooking = async (data: ManualBookingData) => {
    try {
      // Use the first available booking page or the currently selected one
      const selectedBookingPageId = bookingPageId || (bookingPages.length > 0 ? bookingPages[0]._id : 'default-page-id')

      const payload = {
        customerName: data.customerName,
        customerPhone: data.customerPhone,
        customerEmail: '', // Optional field
        bookingPageId: selectedBookingPageId,
        bookingDate: new Date(data.bookingTime).toISOString().split('T')[0],
        bookingSlots: [
          {
            date: new Date(data.bookingTime).toISOString().split('T')[0],
            field: 'Manual Field',
            time: new Date(data.bookingTime).toLocaleTimeString('vi-VN', {
              hour: '2-digit',
              minute: '2-digit',
            }),
          },
        ],
        paymentMethod: 'COD',
        quantity: 1,
        notes: data.notes,
      }

      await bookingAPIs.createManualBooking(payload)
      setOpenManualDialog(false)
      toast.success('Tạo đặt lịch thành công!')
      fetchBookings() // Refresh the list
    } catch (err: any) {
      console.error('Error creating manual booking:', err)
      toast.error('Không thể tạo đặt lịch')
    }
  }

  // Action handlers
  const handleAcceptAction = async (id: string) => {
    await updateBookingStatus(id, 'confirmed')
  }

  const handleRejectAction = async (id: string) => {
    await updateBookingStatus(id, 'rejected')
  }

  const handleViewDetailsAction = (id: string) => {
    // TODO: Navigate to booking detail page
    console.log(`View booking details: ${id}`)
    // window.location.href = `/admin/bookings/${id}`
  }

  const handleClearFilters = () => {
    setSearchQuery('')
    setStatusFilter('all')
    if (setBookingPageId) {
      setBookingPageId('')
    }
  }

  return (
    <div className="max-w-7xl mx-auto py-4 px-2 sm:px-4 md:px-8">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">Quản lý Đặt Lịch</h1>
          <p className="text-gray-600 mt-1">
            Tổng cộng {totalBookings} đặt lịch
          </p>
        </div>
        <Dialog open={openManualDialog} onOpenChange={setOpenManualDialog}>
          <DialogTrigger asChild>
            <Button onClick={() => setOpenManualDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Tạo đặt lịch mới
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Tạo đặt lịch thủ công</DialogTitle>
            </DialogHeader>
            <ManualBookingForm
              onSubmit={handleManualBooking}
              onCancel={() => setOpenManualDialog(false)}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* Filters */}
      <BookingFilters
        searchQuery={searchQuery}
        setSearchQuery={setSearchQuery}
        statusFilter={statusFilter}
        setStatusFilter={setStatusFilter}
        bookingPageId={bookingPageId}
        setBookingPageId={setBookingPageId}
        dateRange={dateRange}
        setDateRange={handleDateRangeChange}
        viewMode={viewMode}
        setViewMode={handleViewModeChange}
        bookingPages={bookingPages}
        isLoadingBookingPages={isLoadingBookingPages}
      />

      {/* Results */}
      <div className="bg-white rounded-lg shadow">
        <div className="p-4 border-b">
          <div className="flex justify-between items-center">
            <h2 className="text-lg font-medium">
              Danh sách đặt lịch ({bookings.length})
            </h2>
            <div className="text-sm text-gray-500">
              Trang {currentPage} / {totalPages}
            </div>
          </div>
        </div>

        <div className="p-4">
          <BookingList
            bookings={bookings}
            isLoading={isLoading}
            error={error}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={setCurrentPage}
            onRetry={fetchBookings}
            onAcceptAction={handleAcceptAction}
            onRejectAction={handleRejectAction}
            onViewDetailsAction={handleViewDetailsAction}
            onCreateNew={() => setOpenManualDialog(true)}
            onClearFilters={handleClearFilters}
            hasFilters={hasFilters}
          />
        </div>
      </div>
    </div>
  )
}

export default ManageBookingsScreen
