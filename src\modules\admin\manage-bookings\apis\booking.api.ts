import type { BookingItem } from '../types'
import mainApi from '@/apis/mainApi'

export interface GetBookingsParams {
  bookingPageId?: string
  status?: string
  startDate?: string
  endDate?: string
  limit?: number
  offset?: number
  search?: string
}

export interface GetBookingsResponse {
  bookings: BookingItem[]
  total: number
  limit: number
  offset: number
}

export interface UpdateBookingStatusPayload {
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed' | 'rejected'
}

export interface CreateManualBookingPayload {
  customerName: string
  customerPhone: string
  customerEmail?: string
  bookingPageId: string
  bookingDate: string
  bookingSlots: {
    date: string
    field: string
    time: string
  }[]
  paymentMethod: string
  quantity?: number
  notes?: string
}

export const bookingAPIs = {
  // Get all bookings with filters
  getBookings: (params?: GetBookingsParams) => 
    mainApi.get<GetBookingsResponse>('/agent/booking', { params }),

  // Get bookings for a specific booking page
  getBookingsByPageId: (bookingPageId: string, params?: Omit<GetBookingsParams, 'bookingPageId'>) =>
    mainApi.get<GetBookingsResponse>(`/agent/booking`, { 
      params: { ...params, bookingPageId } 
    }),

  // Update booking status
  updateBookingStatus: (bookingId: string, data: UpdateBookingStatusPayload) =>
    mainApi.put(`/agent/booking/${bookingId}/status`, data),

  // Create manual booking
  createManualBooking: (data: CreateManualBookingPayload) =>
    mainApi.post<BookingItem>('/agent/booking/manual', data),

  // Get booking details
  getBookingById: (bookingId: string) =>
    mainApi.get<BookingItem>(`/agent/booking/${bookingId}`),

  // Delete booking
  deleteBooking: (bookingId: string) =>
    mainApi.delete(`/agent/booking/${bookingId}`),
}
