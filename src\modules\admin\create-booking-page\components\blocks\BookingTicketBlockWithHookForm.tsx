import type { CreateBookingPayload } from '@/modules/booking/booking.apis'
import type { BookingTicketBlock } from '../../types/blocks'
import { Button } from '@/components/ui/button'
import { Checkbox } from '@/components/ui/checkbox'
import { useBookingPageDetailStore } from '@/modules/admin/stores/booking-page-detail.store'
import { bookingAPIs } from '@/modules/booking/booking.apis'
import { useBookingSlotsStore } from '@/modules/booking/stores/booking-slots.store'
import { appPaths } from '@/utils/app-routes'
import { getFieldName } from '@/utils/field-booking'
import { formatDateYMD } from '@/utils/time'
import { zodResolver } from '@hookform/resolvers/zod'
import { useParams, useRouter } from 'next/navigation'
import React from 'react'
import { useForm } from 'react-hook-form'
import { toast } from 'sonner'
import { z } from 'zod'
import { PAYMENT_OPTIONS } from '../BookingPageConfig/schema/eventConfigSchema'
import { useClearBookings, useSelectedBookings } from './AvailabilityCalendar/store/availabilityCalendarStore'

export interface BookingTicketBlockProps {
  title?: string
  subtitle?: string
  showPriceBreakdown?: boolean
  showBookingSummary?: boolean
  ctaButtonText?: string
  fields?: string[]
  paymentMethods?: string[]
  isPreview?: boolean
}

// Default values for props
const DEFAULT_FIELDS = ['name', 'email', 'phone']
const DEFAULT_PAYMENT_METHODS = ['COD']

/**
 * BookingTicketBlock Component with React Hook Form
 *
 * Displays booking summary and checkout form
 */
export const BookingTicketBlockComponent: React.FC<BookingTicketBlockProps> = ({
  title = 'Đặt vé',
  subtitle = 'Hoàn tất thông tin đặt vé của bạn',
  showPriceBreakdown: _showPriceBreakdown = true, // Renamed with underscore to indicate it's not used
  showBookingSummary = true,
  ctaButtonText = 'Đặt vé ngay',
  fields = DEFAULT_FIELDS,
  paymentMethods = DEFAULT_PAYMENT_METHODS,
  isPreview = false,
}) => {
  // Get bookings from the store
  const selectedBookings = useSelectedBookings()
  const clearBookings = useClearBookings()
  const params = useParams()
  const router = useRouter()
  const slug = params.slug as string

  // Get reload function from booking slots store
  const reloadCurrentSlots = useBookingSlotsStore(state => state.reloadCurrentSlots)

  // Define form validation schema with Zod
  const formSchema = z.object({
    customerName: fields.includes('name')
      ? z.string().min(1, { message: 'Vui lòng nhập họ tên' })
      : z.string().optional(),
    customerEmail: fields.includes('email')
      ? z.string().min(1, { message: 'Vui lòng nhập email' }).email({ message: 'Email không hợp lệ' })
      : z.string().optional(),
    customerPhone: fields.includes('phone')
      ? z.string().min(1, { message: 'Vui lòng nhập số điện thoại' }).regex(/^\d{10,11}$/, { message: 'Số điện thoại không hợp lệ' })
      : z.string().optional(),
    quantity: fields.includes('quantity')
      ? z.number().min(1, { message: 'Số lượng vé phải ít nhất là 1' })
      : z.number().optional(),
    paymentMethod: z.string().min(1, { message: 'Vui lòng chọn phương thức thanh toán' }),
  })

  // Define form type from schema
  type FormValues = z.infer<typeof formSchema>

  // Initialize React Hook Form
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    setValue,
    watch,
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      customerName: '',
      customerEmail: '',
      customerPhone: '',
      quantity: 1,
      paymentMethod: paymentMethods[0] || 'COD',
    },
  })

  // Watch payment method for radio buttons
  const watchPaymentMethod = watch('paymentMethod')

  // Handle form submission
  const onSubmit = async (data: FormValues) => {
    // Validate if booking slots are selected
    if (!selectedBookings || selectedBookings.length === 0) {
      toast.error('Vui lòng kiểm tra lại thông tin', {
        description: 'Vui lòng chọn ít nhất một khung giờ đặt chỗ',
      })
      return
    }

    // If in preview mode (create/edit booking page), don't actually submit the booking
    if (isPreview) {
      toast.info('Đây chỉ là bản xem trước', {
        description: 'Chức năng đặt chỗ không hoạt động trong chế độ xem trước.',
      })
      return
    }

    // Show loading toast
    const loadingToast = toast.loading('Đang xử lý đặt chỗ...', {
      description: 'Vui lòng đợi trong giây lát',
    })

    try {
      const bookingPage = useBookingPageDetailStore.getState()?.bookingPage

      if (!bookingPage?._id) {
        toast.error('Đã xảy ra lỗi khi đặt chỗ', {
          description: 'Vui lòng thử lại sau hoặc liên hệ với chúng tôi.',
        })
        toast.dismiss(loadingToast)
        return
      }

      // Prepare booking data
      const bookingData: CreateBookingPayload = {
        slug,
        bookingPageId: bookingPage._id,
        bookingDate: formatDateYMD(selectedBookings[0]!.date),
        customerName: data.customerName || '',
        customerEmail: data.customerEmail || '',
        customerPhone: data.customerPhone || '',
        bookingSlots: selectedBookings.map(slot => ({
          date: formatDateYMD(slot.date),
          field: slot.field,
          fieldName: getFieldName(slot.field),
          time: slot.time,
        })),
        paymentMethod: data.paymentMethod,
        quantity: data.quantity,
      }

      // Submit booking
      const response = await bookingAPIs.createBooking(bookingData)

      if (response?.status?.success) {
        // Show success message
        toast.success('Đặt chỗ thành công!', {
          description: 'Bạn sẽ được chuyển đến trang chi tiết đặt chỗ.',
        })

        // Reset form
        reset()

        // Clear selected bookings
        clearBookings()

        // Reload booked slots to reflect the new booking
        try {
          await reloadCurrentSlots()
        } catch (reloadError) {
          console.warn('Failed to reload slots after booking:', reloadError)
          // Don't show error to user as the booking was successful
        }

        // Redirect to booking detail page
        if (response?.data?._id) {
          router.push(appPaths.public.bookingDetail(response.data._id))
        }
      } else {
        // Show error message
        toast.error('Đặt chỗ không thành công', {
          description: response?.status?.message || 'Vui lòng thử lại sau.',
        })
      }
    } catch (error) {
      console.error('Error creating booking:', error)

      // Dismiss loading toast

      toast.error('Đã xảy ra lỗi khi đặt chỗ', {
        description: 'Vui lòng thử lại sau hoặc liên hệ với chúng tôi.',
      })
    } finally {
      toast.dismiss(loadingToast)
    }
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm border">
      <h2 className="text-xl font-semibold mb-2">{title}</h2>
      {subtitle && <p className="text-sm text-gray-500 mb-6">{subtitle}</p>}

      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Booking Summary Section - Always visible but with different states */}
        <div className="mb-8">
          <h3 className="text-md font-medium mb-3 flex items-center">
            <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full mr-2 text-sm">1</span>
            Thông tin đặt chỗ
          </h3>

          {showBookingSummary && selectedBookings && selectedBookings.length > 0
            ? (
                <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
                  <BookingSummary selectedBookings={selectedBookings} />
                </div>
              )
            : (
                <div className="p-5 border border-dashed border-gray-300 rounded-lg bg-gray-50 text-center">
                  <p className="text-gray-600 font-medium">Chưa có lịch đặt nào được chọn</p>
                  <p className="text-sm text-gray-500 mt-1">Vui lòng chọn thời gian từ lịch để tiếp tục</p>
                </div>
              )}
        </div>

        {/* Customer Information Section */}
        <div className="mb-8">
          <h3 className="text-md font-medium mb-3 flex items-center">
            <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full mr-2 text-sm">2</span>
            Thông tin khách hàng
          </h3>

          <div className="bg-white border border-gray-200 rounded-lg p-5">
            <div className="space-y-4">
              {/* Mỗi field hiển thị trên một dòng riêng biệt */}
              {fields.includes('name') && (
                <div className="w-full">
                  <label className="block text-sm font-medium mb-1.5 text-gray-700">Họ tên</label>
                  <input
                    type="text"
                    className={`w-full p-3 border ${errors.customerName ? 'border-red-500' : 'border-gray-300'} rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors`}
                    placeholder="Nhập họ tên của bạn"
                    {...register('customerName')}
                  />
                  {errors.customerName && (
                    <p className="mt-1 text-sm text-red-500">{errors.customerName.message}</p>
                  )}
                </div>
              )}

              {fields.includes('email') && (
                <div className="w-full">
                  <label className="block text-sm font-medium mb-1.5 text-gray-700">Email</label>
                  <input
                    type="email"
                    className={`w-full p-3 border ${errors.customerEmail ? 'border-red-500' : 'border-gray-300'} rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors`}
                    placeholder="Nhập email của bạn"
                    {...register('customerEmail')}
                  />
                  {errors.customerEmail && (
                    <p className="mt-1 text-sm text-red-500">{errors.customerEmail.message}</p>
                  )}
                </div>
              )}

              {fields.includes('phone') && (
                <div className="w-full">
                  <label className="block text-sm font-medium mb-1.5 text-gray-700">Số điện thoại</label>
                  <input
                    type="tel"
                    className={`w-full p-3 border ${errors.customerPhone ? 'border-red-500' : 'border-gray-300'} rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors`}
                    placeholder="Nhập số điện thoại của bạn"
                    {...register('customerPhone')}
                  />
                  {errors.customerPhone && (
                    <p className="mt-1 text-sm text-red-500">{errors.customerPhone.message}</p>
                  )}
                </div>
              )}

              {fields.includes('quantity') && (
                <div className="w-full">
                  <label className="block text-sm font-medium mb-1.5 text-gray-700">Số lượng vé</label>
                  <input
                    type="number"
                    className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                    min="1"
                    {...register('quantity', { valueAsNumber: true })}
                  />
                  {errors.quantity && (
                    <p className="mt-1 text-sm text-red-500">{errors.quantity.message}</p>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Payment Method Section */}
        {paymentMethods && paymentMethods.length > 0 && (
          <div className="mb-8">
            <h3 className="text-md font-medium mb-3 flex items-center">
              <span className="inline-flex items-center justify-center w-6 h-6 bg-blue-100 text-blue-600 rounded-full mr-2 text-sm">3</span>
              Phương thức thanh toán
            </h3>

            <div className="bg-white border border-gray-200 rounded-lg p-5">
              <div className="space-y-3">
                {paymentMethods.map(method => (
                  <div key={method} className="relative">
                    <input
                      type="radio"
                      id={method}
                      value={method}
                      {...register('paymentMethod')}
                      className="peer absolute opacity-0 w-full h-full cursor-pointer"
                      onChange={() => setValue('paymentMethod', method)}
                    />
                    <label
                      htmlFor={method}
                      className="flex items-center p-3 border border-gray-300 rounded-md peer-checked:border-blue-500 peer-checked:bg-blue-50 transition-colors cursor-pointer w-full"
                    >
                      <div className="w-5 h-5 border border-gray-400 rounded-full flex items-center justify-center mr-3 peer-checked:border-blue-500">
                        <div className={`w-3 h-3 bg-blue-500 rounded-full ${watchPaymentMethod === method ? 'opacity-100' : 'opacity-0'}`}></div>
                      </div>
                      <span className="font-medium">{method}</span>
                    </label>
                  </div>
                ))}
              </div>
              {errors.paymentMethod && (
                <p className="mt-1 text-sm text-red-500">{errors.paymentMethod.message}</p>
              )}
            </div>
          </div>
        )}

        {/* Submit Button - Fixed at bottom on mobile, normal position on desktop */}
        <div className="mt-8">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 rounded-md font-medium text-base transition-colors shadow-sm disabled:opacity-70 disabled:cursor-not-allowed"
          >
            {isSubmitting ? 'Đang xử lý...' : ctaButtonText}
          </Button>
        </div>
      </form>
    </div>
  )
}

// Config component for the booking ticket block
export const BookingTicketBlockConfig: React.FC<{
  data: BookingTicketBlock['data']
  onChange: (data: BookingTicketBlock['data']) => void
}> = ({ data, onChange }) => {
  // Available field options
  const FIELD_OPTIONS = [
    { id: 'name', label: 'Họ tên' },
    { id: 'email', label: 'Email' },
    { id: 'phone', label: 'Số điện thoại' },
    { id: 'quantity', label: 'Số lượng vé' },
  ]

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Cấu hình Đặt vé</h3>

      <div>
        <label className="block text-sm font-medium mb-1">Tiêu đề</label>
        <input
          type="text"
          className="w-full p-2 border rounded-md"
          value={data.title || ''}
          onChange={e => onChange({ ...data, title: e.target.value })}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Mô tả</label>
        <input
          type="text"
          className="w-full p-2 border rounded-md"
          value={data.subtitle || ''}
          onChange={e => onChange({ ...data, subtitle: e.target.value })}
        />
      </div>

      <div>
        <label className="block text-sm font-medium mb-1">Nút CTA</label>
        <input
          type="text"
          className="w-full p-2 border rounded-md"
          value={data.ctaButtonText || ''}
          onChange={e => onChange({ ...data, ctaButtonText: e.target.value })}
        />
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="show-booking-summary"
          checked={data.showBookingSummary}
          onCheckedChange={(checked: boolean) => onChange({ ...data, showBookingSummary: checked })}
        />
        <label htmlFor="show-booking-summary" className="text-sm">
          Hiển thị thông tin đặt chỗ
        </label>
      </div>

      <div className="flex items-center space-x-2">
        <Checkbox
          id="show-price-breakdown"
          checked={data.showPriceBreakdown}
          onCheckedChange={(checked: boolean) => onChange({ ...data, showPriceBreakdown: checked })}
        />
        <label htmlFor="show-price-breakdown" className="text-sm">
          Hiển thị chi tiết giá
        </label>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Trường thông tin</label>
        <div className="space-y-2">
          {FIELD_OPTIONS.map(option => (
            <div key={option.id} className="flex items-center">
              <Checkbox
                id={`field-${option.id}`}
                checked={data.fields?.includes(option.id)}
                onCheckedChange={(checked: boolean) => {
                  if (checked) {
                    onChange({
                      ...data,
                      fields: [...(data.fields || []), option.id],
                    })
                  } else {
                    onChange({
                      ...data,
                      fields: (data.fields || []).filter(id => id !== option.id),
                    })
                  }
                }}
              />
              <label htmlFor={`field-${option.id}`} className="ml-2 text-sm">
                {option.label}
              </label>
            </div>
          ))}
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium mb-2">Phương thức thanh toán</label>
        <div className="space-y-2">
          {PAYMENT_OPTIONS.map(option => (
            <div key={option.id} className="flex items-center">
              <Checkbox
                id={`payment-${option.id}`}
                checked={data.paymentMethods?.includes(option.id)}
                onCheckedChange={(checked: boolean) => {
                  if (checked) {
                    onChange({
                      ...data,
                      paymentMethods: [...(data.paymentMethods || []), option.id],
                    })
                  } else {
                    onChange({
                      ...data,
                      paymentMethods: (data.paymentMethods || []).filter(id => id !== option.id),
                    })
                  }
                }}
              />
              <label htmlFor={`payment-${option.id}`} className="ml-2 text-sm">
                {option.label}
              </label>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

// BookingSummary component
const BookingSummary: React.FC<{ selectedBookings: any }> = ({ selectedBookings }) => {
  // Group bookings by field
  const bookingsByField = React.useMemo(() => {
    const grouped = new Map()

    selectedBookings.forEach((slot: any) => {
      if (!grouped.has(slot.field)) {
        grouped.set(slot.field, [])
      }
      grouped.get(slot.field).push(slot)
    })

    return grouped
  }, [selectedBookings])

  if (!selectedBookings || selectedBookings.length === 0) {
    return null
  }

  // Format date in Vietnamese - simplified format
  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString('vi-VN', {
      weekday: 'long',
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
    })
  }

  return (
    <div className="text-sm">
      {/* Simple summary table */}
      <table className="w-full">
        <tbody>
          {/* Date row */}
          <tr>
            <td className="py-2 text-gray-600 font-medium">Ngày:</td>
            <td className="py-2 text-right font-medium text-blue-700">
              {selectedBookings.length > 0
                ? formatDate(new Date(selectedBookings[0].date))
                : ''}
            </td>
          </tr>

          {/* Separator */}
          <tr>
            <td colSpan={2} className="border-b border-gray-200"></td>
          </tr>

          {/* Field and time slots */}
          {Array.from(bookingsByField.entries()).map(([fieldId, slots]: [string, any[]]) => (
            <React.Fragment key={fieldId}>
              <tr>
                <td className="py-2 text-gray-600">{getFieldName(fieldId)}</td>
                <td className="py-2 text-right">
                  <div className="flex flex-wrap justify-end gap-1">
                    {slots.map((slot: any) => (
                      <span
                        key={`${slot.field}-${slot.time}`}
                        className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded"
                      >
                        {slot.time}
                      </span>
                    ))}
                  </div>
                </td>
              </tr>
              {/* Separator between fields */}
              <tr>
                <td colSpan={2} className="border-b border-gray-200"></td>
              </tr>
            </React.Fragment>
          ))}
        </tbody>
      </table>
    </div>
  )
}
