import type { BookingCardProps } from '../types'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { format } from 'date-fns'
import { vi } from 'date-fns/locale'
import { Calendar, Clock, MapPin, Phone, User } from 'lucide-react'
import React from 'react'

const statusConfig = {
  pending: { label: 'Chờ xác nhận', color: 'bg-yellow-100 text-yellow-800' },
  confirmed: { label: 'Đã xác nhận', color: 'bg-green-100 text-green-800' },
  cancelled: { label: 'Đã hủy', color: 'bg-red-100 text-red-800' },
  completed: { label: 'Hoàn thành', color: 'bg-blue-100 text-blue-800' },
  rejected: { label: 'Từ chối', color: 'bg-gray-100 text-gray-800' },
}

/**
 * Component to display individual booking information
 */
export const BookingCard = ({
  booking,
  onAcceptAction,
  onRejectAction,
  onViewDetailsAction,
}: BookingCardProps) => {
  const statusInfo = statusConfig[booking.status]

  // Format booking slots for display
  const formatBookingSlots = () => {
    if (!booking.bookingSlots || booking.bookingSlots.length === 0) {
      return 'Không có thông tin slot'
    }

    // Group by date
    const groupedByDate = booking.bookingSlots.reduce((acc, slot) => {
      const date = slot.date
      if (!acc[date]) {
        acc[date] = []
      }
      acc[date].push(`${slot.fieldName || slot.field} (${slot.time})`)
      return acc
    }, {} as Record<string, string[]>)

    return Object.entries(groupedByDate).map(([date, slots]) => (
      <div key={date} className="text-sm">
        <span className="font-medium">
          {format(new Date(date), 'dd/MM/yyyy', { locale: vi })}:
        </span>
        <span className="ml-2">{slots.join(', ')}</span>
      </div>
    ))
  }

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
              <User className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-lg">{booking.customerName}</h3>
              <p className="text-sm text-gray-600">{booking.bookingPageName || `Trang đặt lịch: ${booking.bookingPageId}`}</p>
            </div>
          </div>
          <Badge className={statusInfo.color}>
            {statusInfo.label}
          </Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-3">
        {/* Contact Information */}
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <Phone className="w-4 h-4" />
          <span>{booking.customerPhone}</span>
          {booking.customerEmail && (
            <>
              <span>•</span>
              <span>{booking.customerEmail}</span>
            </>
          )}
        </div>

        {/* Booking Date */}
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <Calendar className="w-4 h-4" />
          <span>
            Ngày đặt: {format(new Date(booking.bookingDate), 'dd/MM/yyyy', { locale: vi })}
          </span>
        </div>

        {/* Booking Slots */}
        <div className="flex items-start space-x-2 text-sm text-gray-600">
          <MapPin className="w-4 h-4 mt-0.5" />
          <div className="flex-1">
            {formatBookingSlots()}
          </div>
        </div>

        {/* Created At */}
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <Clock className="w-4 h-4" />
          <span>
            Tạo lúc: {format(new Date(booking.createdAt), 'dd/MM/yyyy HH:mm', { locale: vi })}
          </span>
        </div>

        {/* Payment Method */}
        <div className="text-sm text-gray-600">
          <span className="font-medium">Thanh toán:</span> {booking.paymentMethod}
          {booking.quantity && booking.quantity > 1 && (
            <span className="ml-2">• Số lượng: {booking.quantity}</span>
          )}
        </div>

        {/* Notes */}
        {booking.notes && (
          <div className="text-sm text-gray-600">
            <span className="font-medium">Ghi chú:</span> {booking.notes}
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex space-x-2 pt-3 border-t">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onViewDetailsAction(booking._id)}
            className="flex-1"
          >
            Xem chi tiết
          </Button>

          {booking.status === 'pending' && (
            <>
              <Button
                variant="default"
                size="sm"
                onClick={() => onAcceptAction(booking._id)}
                className="bg-green-600 hover:bg-green-700"
              >
                Xác nhận
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => onRejectAction(booking._id)}
              >
                Từ chối
              </Button>
            </>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

export default BookingCard
