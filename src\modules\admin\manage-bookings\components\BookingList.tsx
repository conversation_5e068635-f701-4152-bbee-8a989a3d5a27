import type { BookingListProps } from '../types'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { AlertCircle, RefreshCw } from 'lucide-react'
import React from 'react'
import BookingCard from './BookingCard'
import BookingCardSkeleton from './BookingCardSkeleton'
import BookingPagination from './BookingPagination'
import EmptyState from './EmptyState'

/**
 * Component to display a list of bookings with loading, error and empty states
 */
export const BookingList = ({
  bookings,
  isLoading,
  error,
  currentPage,
  totalPages,
  onPageChange,
  onRetry,
  onAcceptAction,
  onRejectAction,
  onViewDetailsAction,
  onCreateNew,
  onClearFilters,
  hasFilters,
}: BookingListProps) => {
  if (isLoading) {
    return (
      <div className="space-y-4">
        <BookingCardSkeleton />
        <BookingCardSkeleton />
        <BookingCardSkeleton />
        <BookingCardSkeleton />
        <BookingCardSkeleton />
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Lỗi tải dữ liệu</AlertTitle>
        <AlertDescription className="mt-2">
          {error}
          <Button
            variant="outline"
            size="sm"
            onClick={onRetry}
            className="mt-2 ml-2"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Thử lại
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  if (bookings.length === 0) {
    return (
      <EmptyState
        hasFilters={hasFilters}
        onCreateNew={onCreateNew}
        onClearFilters={onClearFilters}
      />
    )
  }

  return (
    <div className="space-y-4">
      {/* Booking Cards */}
      <div className="space-y-4">
        {bookings.map((booking) => (
          <BookingCard
            key={booking._id}
            booking={booking}
            onAcceptAction={onAcceptAction}
            onRejectAction={onRejectAction}
            onViewDetailsAction={onViewDetailsAction}
          />
        ))}
      </div>

      {/* Pagination */}
      <BookingPagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={onPageChange}
      />
    </div>
  )
}

export default BookingList
